aws_region  = "eu-west-2"
aws_profile = "092595024884"
env         = "prod"

public_fqdn = "api.mysmash.media"

service_count              = "1"
service_name               = "api"
service_port               = "3000"
service_memory_reservation = "64"
service_memory             = "384"
service_cpu                = "128"
ulimit_nofile              = "1024"

# ENV vars
env_vars = {
  ALLOWED_ORIGIN = {
    type  = "String"
    value = "https://app.mysmash.media,https://ops.mysmash.media"
  }

  APP_BASE_URL = {
    type  = "String"
    value = "api.mysmash.media"
  }

  APP_HOST = {
    type  = "String"
    value = "0.0.0.0"
  }

  APP_PORT = {
    type  = "String"
    value = "3000"
  }

  APP_PROTOTYPE = {
    type  = "String"
    value = "https"
  }

  APP_VERSION = {
    type  = "String"
    value = "1.0.0"
  }

  APP_ENV = {
    type  = "String"
    value = "live"
  }

  AUTH0_DOMAIN = {
    type  = "String"
    value = "mysmash.eu.auth0.com"
  }

  AUTH_CHECK = {
    type  = "String"
    value = "https://roles/role"
  }

  AWS_BUCKET_NAME = {
    type  = "String"
    value = "im-files-prod-20200924143011010000000001"
  }

  AWS_REGION = {
    type  = "String"
    value = "eu-west-2"
  }

  CACHE_EXPIRY = {
    type  = "String"
    value = "129600000"
  }

  CACHE_MAX_STORE = {
    type  = "String"
    value = "500"
  }

  DB_URL = {
    type  = "SecureString"
    value = "AQICAHiboL09oVyDmfY/NdusL0meAEoT6f5m4qeDGUJCltfD9AHNaK1MvlrZgbMVEsTfYqGeAAAAtDCBsQYJKoZIhvcNAQcGoIGjMIGgAgEAMIGaBgkqhkiG9w0BBwEwHgYJYIZIAWUDBAEuMBEEDBoxT1tolzEfnlI9SAIBEIBtyrBRPfuBOVLMfHW63vpyeeT4fRwSDqW6jEaxB1zBxCoi4pt/C391HzOQK0Y2Rh3FgzHkE76APyy9v8KZ9Z/ard0FUyV54AejQVBex+wJEOJSfPWTlE3aQDgIfJFwLd59KopEqdyLhcyO44Rq0w=="
  }

  DEBUG_LOG = {
    type  = "String"
    value = "true"
  }

  DEFAULT_API_VERSION = {
    type  = "String"
    value = "1"
  }

  IS_ELASTIC_DISABLE = {
    type  = "String"
    value = "false"
  }

  ELASTIC_API_ID = {
    type  = "String"
    value = "rGoI33QBZ24pb6YyUbSw"
  }

  ELASTIC_API_KEY = {
    type  = "SecureString"
    value = "AQICAHiboL09oVyDmfY/NdusL0meAEoT6f5m4qeDGUJCltfD9AE0GXePtULcWAt1/qjZcJyuAAAAdDByBgkqhkiG9w0BBwagZTBjAgEAMF4GCSqGSIb3DQEHATAeBglghkgBZQMEAS4wEQQMgglilxfRiyLqvGAeAgEQgDHRRcKG/ze18gq8x5NXly02M19/EAPkJ6hV6ridBQWRpt8t6le0HAvmEkT9irN0YSL+"
  }

  ELASTIC_APM_SECRET_TOKEN = {
    type  = "SecureString"
    value = "AQICAHiboL09oVyDmfY/NdusL0meAEoT6f5m4qeDGUJCltfD9AEckuo9+czkz2ayjCNwiCCSAAAAbTBrBgkqhkiG9w0BBwagXjBcAgEAMFcGCSqGSIb3DQEHATAeBglghkgBZQMEAS4wEQQMN8og2FPsBo4p3butAgEQgCpiTfcOi7q820kXMXuE74p3SvO4ETvqwYdUNEez5Qso5T0NA0oAs3HmOeA="
  }

  ELASTIC_APM_SERVER_URL = {
    type  = "String"
    value = "to_be_set_later"
  }

  ELASTIC_APM_SERVICE_NAME = {
    type  = "String"
    value = "SMASH_API_ON_PROD"
  }

  ELASTIC_SEARCH_INDEX = {
    type  = "String"
    value = "smash_prod"
  }

  ELASTIC_SEARCH_URL = {
    type  = "String"
    value = "https://1f70e1cf5c3a4ee98ee7abae9e9d8926.eu-west-1.aws.found.io:9243"
  }

  IDENTITY_MANGER_URL = {
    type  = "String"
    value = "https://api.im.mysmash.media"
  }

  IGNORE_URLS_LOG = {
    type  = "String"
    value = "/v1/health"
  }

  IMG_COMPRESS_LEVEL = {
    type  = "String"
    value = "9"
  }

  IMG_QUALITY = {
    type  = "String"
    value = "60"
  }

  INIT_RECORD = {
    type  = "String"
    value = "10"
  }

  IS_APM = {
    type  = "String"
    value = "false"
  }

  LOG_PAYLOAD = {
    type  = "String"
    value = "false"
  }

  LOG_REQUEST_COMPLETE = {
    type  = "String"
    value = "false"
  }

  LOG_REQUEST_START = {
    type  = "String"
    value = "false"
  }

  M2M_API_URL = {
    type  = "String"
    value = "https://mysmash.eu.auth0.com/oauth/token"
  }

  M2M_CLIENT_ID = {
    type  = "String"
    value = "saGH8M3Ozi2DeVUR7KitJ02t71X6qy76"
  }

  M2M_CLIENT_SECRET = {
    type  = "SecureString"
    value = "AQICAHiboL09oVyDmfY/NdusL0meAEoT6f5m4qeDGUJCltfD9AHU1Az/V9qlPzajj22GWYqVAAAAojCBnwYJKoZIhvcNAQcGoIGRMIGOAgEAMIGIBgkqhkiG9w0BBwEwHgYJYIZIAWUDBAEuMBEEDLIpxDRUwh9Gzr/k9gIBEIBbIpZr/mps1Jq9zf4fvZIkTITEiwOaLFpxw70b6NdaIK0aso6pQEGBDbYFLCn1vbRGiCQ1eVrugreX9dPruDnzbfCpDcNHvdl0hahVHWr1DPbgMEBgJfHdKSXPlw=="
  }

  MAX_IMAGE_SIZE_ARTWORK = {
    type  = "String"
    value = "10485760"
  }

  SKIP_USER_VALIDATION_ON_ROUTES = {
    type  = "String"
    value = "/v1/auth/createUser,/v1/auth,/v1/company/{id},/v1/company/list"
  }

  VALID_API_VERSIONS = {
    type  = "String"
    value = "1,2"
  }

  GETTY_CLIENT_ID = {
    type  = "String"
    value = "c6uy2upr67mfss9dmbnv2pbx"
  }

  GETTY_CLIENT_SECRET = {
    type  = "SecureString"
    value = "AQICAHiboL09oVyDmfY/NdusL0meAEoT6f5m4qeDGUJCltfD9AG2TvrGCnlOoTC6oMssIC/PAAAAcjBwBgkqhkiG9w0BBwagYzBhAgEAMFwGCSqGSIb3DQEHATAeBglghkgBZQMEAS4wEQQMi5f+gJhzW/PvS9RpAgEQgC8OmkE6dIqdS9awxZ83BSbJhHIyUMxn6zl9rIYS7DlCKanO1gvBeHDwdbO/4avPlQ=="
  }

  GETTY_AUTH_URL = {
    type  = "String"
    value = "https://api.gettyimages.com/oauth2/token"
  }

  GETTY_AUTH_REDIRECT_URI = {
    type  = "String"
    value = "https://app.mysmash.media/project/getty/verify"
  }

  AGENDA_DB_URL = {
    type  = "SecureString"
    value = "AQICAHiboL09oVyDmfY/NdusL0meAEoT6f5m4qeDGUJCltfD9AEjtmQDnMjiPveCdURenu4cAAAAszCBsAYJKoZIhvcNAQcGoIGiMIGfAgEAMIGZBgkqhkiG9w0BBwEwHgYJYIZIAWUDBAEuMBEEDNs81S+JcD6Oy8V5lAIBEIBsgD8Vgf0XzAPbv+ovDsa3UpP8LOUfBYREgXQOaIZJ3cbrpWc8cgCJH7VLGx1QTARNKCmxYpFoXI0RLHmsjYl9JwxuvRbZ/jhivZKSNLeLs9YNDNpfrbCU5p4HYBvsTKD8xFCxAWgCart5qvPN"
  }

  WEBAPP_BASE_URL = {
    type  = "String"
    value = "https://app.mysmash.media"
  }

  MANDRILL_API_KEY = {
    type  = "SecureString"
    value = "AQICAHiboL09oVyDmfY/NdusL0meAEoT6f5m4qeDGUJCltfD9AEcfb2ikixSL9TdVLNoi9gmAAAAdDByBgkqhkiG9w0BBwagZTBjAgEAMF4GCSqGSIb3DQEHATAeBglghkgBZQMEAS4wEQQMCuH7zVEHHKIjR8EyAgEQgDEZguCUEaOtCA1m0hL4J7VZCqWeSyqq5zFL6Rv5hNnCUfJIaL9jFqueMLjHcvKVaOYh"
  }

  GETTY_SPREADSHEET_CLIENT_EMAIL={
    type  = "String"
    value = "<EMAIL>"
  }

  GETTY_SPREADSHEET_PRIVATE_KEY={
    type  = "SecureString"
    value = "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"
  }

  GETTY_SPREADSHEET_ID={
    type  = "SecureString"
    value = "AQICAHiboL09oVyDmfY/NdusL0meAEoT6f5m4qeDGUJCltfD9AFk30sqCGfZpJ0E3G7SPANpAAAAizCBiAYJKoZIhvcNAQcGoHsweQIBADB0BgkqhkiG9w0BBwEwHgYJYIZIAWUDBAEuMBEEDBr8YaN+GdxKuyrgAwIBEIBHHfxyloZMBUnO4VDpaCKFc6P4x3rusRbLoB5su51reevAdYNvPSvMhwA8/WW/rt4NAz8TyUhZ2ockZ7H6bHdcoEezYWzf/NY="
  }

  GETTY_SPREADSHEET_RANG={
    type  = "String"
    value = "Sheet1"
  }

  MAILCHIMP_API_KEY={
    type  = "SecureString"
    value = "AQICAHiboL09oVyDmfY/NdusL0meAEoT6f5m4qeDGUJCltfD9AEeNVJIgEz8EdX9BNOi+sjnAAAAhDCBgQYJKoZIhvcNAQcGoHQwcgIBADBtBgkqhkiG9w0BBwEwHgYJYIZIAWUDBAEuMBEEDEV2K3Oy++Si0qhC2gIBEIBASsOggy80StlzDZ5O02ZSPy5Khwic3wbpmASIrZyuP6Uial4sotAnurRclZvgERfICb1v3W6nrudcaiukLetg4w=="
  }

  MAILCHIMP_SERVER_PREFIX={
    type  = "String"
    value = "us17"
  }

  MAILCHIMP_MEMBERS_LIST_ID={
    type  = "String"
    value = "1bb602fb83"
  }

  MAILCHIMP_ENV={
    type  = "String"
    value = "Production"
  }
  
  SOCKET_ALLOWED_ORIGIN = {
    type = "String"
    value = "https://app.mysmash.media"
  }

  CRM_AGENDA_DB_URL = {
    type = "SecureString"
    value = "AQICAHiboL09oVyDmfY/NdusL0meAEoT6f5m4qeDGUJCltfD9AHw6GukPyLBebB6a++5w0ctAAAAvTCBugYJKoZIhvcNAQcGoIGsMIGpAgEAMIGjBgkqhkiG9w0BBwEwHgYJYIZIAWUDBAEuMBEEDAQUodYUBk7l1rAzIQIBEIB2T33QrJT9RB6zaShNv+oJRXz1hB0qsNdVSpTDDnLRctG6Pjld3667TpPAmYFWWTYCRkjbCq1QC8wlsahIb8wKRYkABcvmqnX4qjIoZ4A5eq7lubTXXep9bAj9A7TGp9Jhj6u+/kW5fsI6SJubtXqLLznWljBCww=="
  }

  CUSTOMER_SITE_ID = {
    type = "String"
    value = "c2649c2638e8a9cfd9d5"
  }

  CUSTOMER_API_KEY = {
    type = "SecureString"
    value = "AQICAHiboL09oVyDmfY/NdusL0meAEoT6f5m4qeDGUJCltfD9AGxHgyCjFfY0QxZ/tP/89wnAAAAcjBwBgkqhkiG9w0BBwagYzBhAgEAMFwGCSqGSIb3DQEHATAeBglghkgBZQMEAS4wEQQM3q7/UWehCKiw7b0eAgEQgC9P0cWQx3nuJIr4hA2D98qthhnaiaX4+Or3wrjvQSKl2bJsacACXjbtm1pdU+7qYA=="
  }

  CUSTOMER_GETTY_EMAIL_API_KEY = {
    type = "SecureString"
    value = "AQICAHiboL09oVyDmfY/NdusL0meAEoT6f5m4qeDGUJCltfD9AFDPh3r6XgIs8jVBBwH7WmYAAAAfjB8BgkqhkiG9w0BBwagbzBtAgEAMGgGCSqGSIb3DQEHATAeBglghkgBZQMEAS4wEQQMZLqRJBbjE58faK1oAgEQgDsfjVZluOe2/FeiUqpT+UaxLoZQjjzJyN6MMSP9/NXrGQJAv2OquuWRaCBYg2l0aT8xrZkjSvHPC0As6g=="
  }

  STRIPE_SECRET_KEY = {
    type = "SecureString"
    value = "AQICAHiboL09oVyDmfY/NdusL0meAEoT6f5m4qeDGUJCltfD9AHamc4ivDzVMXS06z1lZzoyAAAAzjCBywYJKoZIhvcNAQcGoIG9MIG6AgEAMIG0BgkqhkiG9w0BBwEwHgYJYIZIAWUDBAEuMBEEDOpw/UJJeFDOpK0bzAIBEICBhlMCXM6K4VtemyVxXtYvMOBBav+kM1XMmTDA3liVsDZk9dDMIRn8qMVQhdAtZ9MTY838nwkjVP/pOpGkpSnXCx5igGVgmrnJd17vbAbZdroPF80ftmtWI5+ETozTi/MjXec1q9Rh175ysP2SXCPRlh5F8vEOAQ08nzEiXSlwHEEvzB0+SnaI"
  }

  STRIPE_WEBHOOK_SECRET = {
    type = "SecureString"
    value = "AQICAHiboL09oVyDmfY/NdusL0meAEoT6f5m4qeDGUJCltfD9AEF9tIT/Hi2EixP9/tUQ/MSAAAAhTCBggYJKoZIhvcNAQcGoHUwcwIBADBuBgkqhkiG9w0BBwEwHgYJYIZIAWUDBAEuMBEEDBDtLNlCCOiNbWYg/AIBEIBBiqYhDl3wyevGob+suA+3Po44ZgnBdyPexXrMHbeLitTprFNxNNT2ar5WIJ/oBRqrcL2xriWvBedNh/UsZo6yFSk="
  }

  STRIPE_PROMOTION_CODE = {
    type = "String"
    value = "promo_1PdZnNHrMi9SfemkfrRj0gFQ"
  }

  ADMIN_EMAILS = {
    type = "String"
    value = "<EMAIL>,<EMAIL>"
  }

  EMAIL_TEMPLATE_DATA = {
    type = "String"
    value = "{\"gettyInfoTemplateId\":\"2\",\"inviteTemplateId\":\"3\",\"welcomeTemplateId\":\"4\",\"salesEstimateTemplateId\":\"5\",\"deleteUserTemplateId\":\"20\",\"accountCreateLinkTemplateId\":\"14\",\"submissionFeedbackTemplateId\":\"23\",\"calloutPublishTemplateId\":\"16\",\"slateTemplateId\":\"17\",\"calloutReadyToReviewTemplateId\":\"18\",\"paymentSucceededTemplateId\":\"19\",\"submissionSubmitEmail\":\"24\",\"rejectSubmissionEmail\":\"25\",\"slateStatusChangeTemplateId\":\"27\"}"
  }

  SALES_EMAIL_ADDRESS = {
    type = "String"
    value = "<EMAIL>"
  }

  NO_REPLAY_EMAIL = {
    type = "String"
    value = "<EMAIL>"
  }

  CUPID_EMAIL_ADDRESS = {
    type = "String"
    value = "<EMAIL>"
  }

  ADMIN_EMAIL_ADDRESS = {
    type = "String"
    value = "<EMAIL>"
  }

  SLACK_CHANNEL = {
    type = "String"
    value = "C07ENL7TV9Q"
  }

  SLACK_TOKEN = {
    type = "SecureString"
    value = "AQICAHiboL09oVyDmfY/NdusL0meAEoT6f5m4qeDGUJCltfD9AGL+8Wo2Kjk9WIKc2bTe/KTAAAAlzCBlAYJKoZIhvcNAQcGoIGGMIGDAgEAMH4GCSqGSIb3DQEHATAeBglghkgBZQMEAS4wEQQMxTZDzx9/JEUF3HhMAgEQgFGFUooAC9K5pJUdDnnUEnnjmGk1FrtA6wMoyu2nqNkltH5ulo/eAI8YZMEY4l07NkdnijMdJJKKWzGE6OYc4IEt0rOLobUWrfPL+IxOwBr3v0w="
  }

  PUBLIC_API_AUTH_KEY = {
    type = "SecureString"
    value = "AQICAHiboL09oVyDmfY/NdusL0meAEoT6f5m4qeDGUJCltfD9AGutvstIzi4VDlZMzwDcd2HAAAAdjB0BgkqhkiG9w0BBwagZzBlAgEAMGAGCSqGSIb3DQEHATAeBglghkgBZQMEAS4wEQQM8licN5pPpPgL9s5dAgEQgDODx4x0GYFkHFJ+UD1zM53vE/88TcGRZnBXIsS9C6LtZCEQQHRoSUV6QxUPXIoXJl6TMFE="
  }
  STRIPE_TRIAL_DAYS = {
    type = "String"
    value = "7"
  }
  X_USER_EMAIL = {
    type = "String"
    value = "<EMAIL>"
  }
}
