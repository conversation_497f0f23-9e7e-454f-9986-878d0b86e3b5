import React, { useState } from 'react';
import PropTypes from 'prop-types';
import { useLayout } from '../../contexts/LayoutContext';
import NewSidebar from '../../sharedComponents/NewSidebar/newsidebar';
import DashboardHeader from '../../sharedComponents/Header/dashboardHeader';
import SubscriptionBanner from '../../sharedComponents/Subscription/SubscriptionBanner';
import { useSelector, useDispatch } from 'react-redux';
import { get } from 'lodash';
import {
  setSubscriptionJourneyModal,
  getReminder,
} from '../../reducer/subscription';
import styles from './MainLayout.module.scss';

/**
 * MainLayout - Layout component with sidebar for most pages
 *
 * @param {Object} props - Component props
 * @param {React.ReactNode} props.children - Child components to render in the layout
 * @param {Object} props.profile - User profile data
 * @param {boolean} props.noAvatar - Whether to hide the avatar in the sidebar
 * @param {boolean} props.noLogoLink - Whether to disable the logo link
 * @returns {JSX.Element} - Rendered component
 */
const MainLayout = ({
  children,
  profile = {},
  noAvatar = false,
  noLogoLink = false,
  pageTitle = null,
}) => {
  const { isSidebarOpen, isMobile } = useLayout();
  const dispatch = useDispatch();
  const [subscriptionCancelled, setSubscriptionCancelled] = useState(false);

  // Get subscription data from Redux store
  const subscriptionReminder = useSelector(
    (state) => state.subscription.subscriptionReminder,
  );
  const subscriptionStatus = useSelector(
    (state) => state.subscription.subscriptionStatus,
  );
  const currentPlan = useSelector((state) => state.subscription.currentPlan);
  const isTrial = useSelector((state) => state.subscription.isTrial);

  // Handle subscription modal
  const handleSubscription = () => {
    dispatch(
      setSubscriptionJourneyModal({
        modalStatus: true,
        feature: 'legacy', // Changed from 'learnSmashPro' to 'legacy' to show Smash Pro upgrade
        onComplete: () => { },
      }),
    );
  };

  // Close subscription reminder
  const closeSubscriptionReminder = () => {
    setSubscriptionCancelled(false);
  };

  // Banner visibility is now handled by the SubscriptionBanner component

  return (
    <div className={styles.layoutRoot}>
      {/* Mobile Header */}
      {isMobile && (
        <div className={styles.mobileHeader}>
          <DashboardHeader profile={profile} />
        </div>
      )}

      <div className={styles.layoutContainer}>
        {/* Sidebar */}
        <div
          className={`${styles.sidebar} ${isSidebarOpen ? styles.sidebarOpen : styles.sidebarClosed}`}
        >
          <NewSidebar
            profile={profile}
            noAvatar={noAvatar}
            noLogoLink={noLogoLink}
          />
        </div>

        {/* Main Content */}
        <div
          className={`${styles.mainContent} ${isSidebarOpen ? styles.contentWithSidebar : styles.contentWithCollapsedSidebar}`}
        >
          {/* Subscription Banner */}
          <div
            style={{
              marginLeft: '-20px',
              marginRight: '-20px',
              width: 'calc(100% + 40px)',
              ...(isMobile && {
                marginLeft: '-16px',
                marginRight: '-16px',
                width: 'calc(100% + 32px)',
              }),
            }}
          >
            <SubscriptionBanner
              subscriptionStatus={subscriptionStatus}
              currentPlan={currentPlan}
              isTrial={isTrial}
              getReminder={() => dispatch(getReminder())}
              handleSubscriptionModal={handleSubscription}
              subscription={profile?.subscription}
              isLegacy={currentPlan === 'legacy'}
              subscriptionReminder={subscriptionReminder}
              subscriptionCancelled={subscriptionCancelled}
              setsubscriptionCancelledStatus={closeSubscriptionReminder}
              isMobile={isMobile}
            />
          </div>

          {pageTitle && (
            <div className={styles.pageTitle}>
              <h1>{pageTitle}</h1>
            </div>
          )}
          {children}
        </div>
      </div>
    </div>
  );
};

MainLayout.propTypes = {
  children: PropTypes.node.isRequired,
  profile: PropTypes.object,
  noAvatar: PropTypes.bool,
  noLogoLink: PropTypes.bool,
  pageTitle: PropTypes.string,
};

export default MainLayout;
