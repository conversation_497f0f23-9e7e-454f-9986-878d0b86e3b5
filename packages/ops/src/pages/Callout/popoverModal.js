import React, { useState, useEffect, useCallback } from 'react';
import Typography from '@mui/material/Typography';
import { Button, Modal, Box, TextField } from '@mui/material';

const PopoverModal = ({
  title,
  open,
  onClose,
  btnText,
  onSubmit,
  placeholder,
  defaultValue,
  reset,
  previousFeedback, // This will now be an array of feedback history
}) => {
  const [text, setText] = useState(defaultValue);

  useEffect(() => {
    if (reset) {
      setText('');
    } else if (defaultValue) {
      setText(defaultValue);
    } else {
      setText('');
    }
  }, [reset, defaultValue]);

  const handleSubmit = useCallback(() => {
    if (onSubmit) {
      onSubmit(text);
    }
  }, [onSubmit, text]);

  const handleModalClose = () => {
    if (defaultValue) {
      setText(defaultValue);
    } else {
      setText('');
    }
    onClose();
  };

  return (
    <Modal
      open={open}
      onClose={onClose}
      aria-labelledby="modal-title"
      aria-describedby="modal-description"
    >
      <Box
        sx={{
          position: 'absolute',
          top: '50%',
          left: '50%',
          transform: 'translate(-50%, -50%)',
          width: 800,
          bgcolor: 'background.paper',
          boxShadow: 24,
          p: 2,
        }}
      >
        {/* Fixed Header */}
        <Typography
          variant="subtitle1"
          className="fw-700 mb-16"
          sx={{ flexShrink: 0 }}
        >
          {title}
        </Typography>

        {/* Previous Feedback Section - Only show if there's previous feedback */}
        {previousFeedback &&
          Array.isArray(previousFeedback) &&
          previousFeedback.length > 0 && (
            <Box sx={{ marginBottom: 3 }}>
              <Typography
                variant="subtitle2"
                sx={{ fontWeight: 'bold', marginBottom: 1 }}
              >
                Previous Feedback ({previousFeedback.length}{' '}
                {previousFeedback.length === 1 ? 'entry' : 'entries'})
              </Typography>
              <Box
                sx={{
                  maxHeight: 200,
                  overflowY: 'auto',
                  border: '1px solid #e0e0e0',
                  borderRadius: 1,
                  backgroundColor: '#f9f9f9',
                }}
              >
                {previousFeedback.map((feedbackEntry, index) => (
                  <Box
                    key={feedbackEntry._id || index}
                    sx={{
                      padding: 2,
                      borderBottom:
                        index < previousFeedback.length - 1
                          ? '1px solid #e0e0e0'
                          : 'none',
                    }}
                  >
                    <Box
                      sx={{
                        display: 'flex',
                        justifyContent: 'space-between',
                        alignItems: 'center',
                        marginBottom: 1,
                      }}
                    >
                      <Typography
                        variant="caption"
                        sx={{ fontWeight: 'bold', color: '#666' }}
                      >
                        Feedback #{previousFeedback.length - index}
                      </Typography>
                      <Typography variant="caption" sx={{ color: '#666' }}>
                        {feedbackEntry.addedAt
                          ? new Date(feedbackEntry.addedAt).toLocaleDateString(
                              'en-US',
                              {
                                year: 'numeric',
                                month: 'short',
                                day: 'numeric',
                                hour: '2-digit',
                                minute: '2-digit',
                              }
                            )
                          : 'Date not available'}
                      </Typography>
                    </Box>
                    <Typography
                      variant="body2"
                      sx={{
                        whiteSpace: 'pre-wrap',
                        fontSize: '0.875rem',
                        lineHeight: 1.5,
                      }}
                    >
                      {feedbackEntry.feedback}
                    </Typography>
                  </Box>
                ))}
              </Box>
            </Box>
          )}

        <TextField
          fullWidth
          multiline
          rows={8}
          variant="outlined"
          onChange={(e) => setText(e.target.value)}
          placeholder={placeholder}
          sx={{ marginBottom: 2 }}
          defaultValue={text || defaultValue}
          value={text}
        />
        <Box
          direction="row"
          spacing={2}
          sx={{ justifyContent: 'end' }}
          display="flex"
          className="justify-content-end"
        >
          <Button
            variant="outlined"
            onClick={handleModalClose}
            className="mr-12"
            sx={{ mr: 2 }}
          >
            Cancel
          </Button>
          <Button variant="contained" color="primary" onClick={handleSubmit}>
            {btnText}
          </Button>
        </Box>
      </Box>
    </Modal>
  );
};

export default PopoverModal;
