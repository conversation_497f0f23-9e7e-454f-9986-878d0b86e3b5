import {
  Show,
  SimpleShowLayout,
  TextField,
  Button,
  FunctionField,
  ChipField,
  useShowController,
  useNotify,
  useDataProvider,
  Confirm,
  useRefresh,
  Link,
  Datagrid,
  List,
  BooleanField,
  useRedirect,
  Title,
  EmailField,
  TopToolbar,
  ExportButton,
} from 'react-admin';
import {
  Box,
  Typography,
  Stack,
  IconButton,
  Checkbox,
  FormControlLabel,
} from '@mui/material';
import {
  Edit,
  Visibility,
  Share,
  Email,
  Delete,
  Add,
  ChatBubbleOutline,
  Close,
} from '@mui/icons-material';
import { get } from 'lodash';

import {
  getStatusBackgroundColor,
  getStatusTextColor,
} from '../../helpers/helper';
import InboxIcon from '@mui/icons-material/Inbox';
import { useEffect, useState } from 'react';
import PopoverModal from './popoverModal';
import StatusField from './slatesStatusDropdown';
import { PostPagination } from '../../helpers/helper';

export const ViewCallout = (props) => {
  const { record, isLoading } = useShowController();
  const [isOpen, setIsOpen] = useState(false);
  const [confirmSubmissionModal, setConfirmSubmissionModal] = useState(false);
  const [editNotes, setEditNotes] = useState(null);
  const [selectedSubmissionId, setSelectedSubmissionId] = useState(null);
  const [isOpenModal, setIsOpenModal] = useState(false);
  const [submissionData, setSubmissionData] = useState(null);
  const [isModeType, setModeType] = useState('');
  const [feedbackData, setFeedbackData] = useState(null);
  const [reset, setReset] = useState(false);
  const [confirmDeleteSlateModal, setConfirmDeleteSlateModal] = useState(false);
  const [deleteSlateId, setDeleteSlateId] = useState(null);
  const [showNewOnly, setShowNewOnly] = useState(false);
  const [calloutSlats, setCalloutSlats] = useState([]);

  // const { data: calloutSlats, loading } = useGetList('calloutSlats');

  const redirect = useRedirect();

  const refresh = useRefresh();
  const notify = useNotify();
  const dataProvider = useDataProvider();

  useEffect(() => {
    if (record?.id) {
      dataProvider
        .getList('calloutSlats', {
          filter: { calloutId: record.id, select: 'slates' },
          pagination: { page: 1, perPage: 50 }, // Adjust perPage as needed
          sort: { field: 'addedAt', order: 'DESC' },
        })
        .then(({ data }) => {
          setCalloutSlats(data);
        })
        .catch((error) => {
          console.error('Error fetching calloutSlats:', error);
        });
    }
  }, [record, dataProvider]);

  if (isLoading) return <p>Loading...</p>;
  if (!record) return <p>No data available</p>;

  const isPublished = record?.isPublished;
  const confirmTitle = isPublished
    ? 'Are you sure you want to unpublish this call out?'
    : 'Are you sure you want to publish this call out?';
  const confirmContent = isPublished
    ? 'Unpublishing this call out will make it no longer display in the Smash platform and stop users submitting their projects.'
    : 'Publishing this call out will display it in the Smash platform and allow users to submit their projects.';

  const handleSendUpgradeEmail = async () => {
    if (!record.discoverer || !record.id) {
      alert('Missing required data for API request.');
      return;
    }
    const payload = {
      email: record.email,
      planId: record.enterPrisePlanId,
      type: 'enterprise',
      priceId: record.enterPrisePriceId,
    };
    try {
      const enterPriseLink = await dataProvider.create(
        'v1/subscription/getStripeCheckoutLink',
        {
          data: payload,
        }
      );
      const discovererEmailPayload = {
        id: record.id,
        buttonLink: enterPriseLink.data.url,
      };
      await dataProvider.create(
        'v1/callout/discovererEmail',
        { data: discovererEmailPayload },
        {
          onSuccess: () => {
            notify('Email sent successfully!', {
              type: 'success',
              multiLine: true,
              autoHideDuration: 2500,
            });
            refresh();

            // Notify after everything is done
            notify('Upgrade email sent successfully!', {
              type: 'success',
              autoHideDuration: 2500,
            });
          },
          onError: (error) => {
            notify(`${String(error)}`, {
              type: 'error',
              multiLine: true,
              autoHideDuration: 2500,
            });
          },
        }
      );
    } catch (error) {
      console.error('API Error:', error);
      notify('Failed to send upgrade email.', {
        type: 'error',
        autoHideDuration: 2500,
      });
    }
  };

  const handleViewCallout = () => {
    window.open(
      `${process.env.REACT_APP_WEBAPP_URL}/callouts/view/${record.id}`,
      '_blank'
    );
  };

  const handleShareCallout = () => {
    const link = `${process.env.REACT_APP_WEBAPP_URL}/callouts/view/${record.id}`;
    navigator.clipboard
      .writeText(link)
      .then(() => {
        notify('Callout link copied successfully', { type: 'success' });
      })
      .catch(() => {
        notify('Failed to copy the link', { type: 'warning' });
      });
  };

  const handleRejectClick = (id) => {
    setSelectedSubmissionId(id); // Store the selected submission ID
    setConfirmSubmissionModal(true); // Open the confirm modal
  };

  const clickHandler = (val, event) => {
    switch (event) {
      case 'addToSlate': {
        setSubmissionData(val);
        setIsOpenModal(true);
        setModeType('addToSlate');
        setReset(false);
        break;
      }
      case 'addFeedback': {
        console.log('Setting feedback data:', val); // Debug log
        setFeedbackData(val);
        setIsOpenModal(true);
        setModeType('addFeedback');
        setReset(false);
        break;
      }
      default: {
        console.log('default event', event);
      }
    }
  };

  const confirmRejectSubmission = () => {
    if (selectedSubmissionId) {
      rejectSubmission(selectedSubmissionId);
    }
    setConfirmSubmissionModal(false);
  };

  const handlePublishCallout = async () => {
    if (!record.discoverer || !record.id) {
      alert('Missing required data for API request.');
      return;
    }

    const payload = {
      isPublished: !isPublished,
    };

    try {
      await dataProvider.update('v1/callout', {
        method: 'PATCH',
        id: record.id,
        data: payload,
      });
      setIsOpen(false);
      refresh();
      notify(`Callout updated successfully!`);
    } catch (error) {
      setIsOpen(false);
      console.error('API Error:', error);
      notify(`Failed to ${isPublished ? 'published' : 'unpublished'} callout.`);
    }
  };

  const rejectSubmission = async (id) => {
    const payload = { id: id, status: 'REJECTED' };
    try {
      await dataProvider.update(`v1/callout`, {
        method: 'PATCH',
        id: record.id,
        endPoint: 'submissionStatus',
        data: payload,
      });
      setIsOpen(false);
      refresh();
      notify(`Submission rejected successfully!`);
    } catch (error) {
      setIsOpen(false);
      console.error('API Error:', error);
      notify(`Failed to reject submission.`);
    }
  };
  // const handleChat = () => {};
  // const handleAdd = () => {};
  const handleView = (records) => {
    window.open(
      `${process.env.REACT_APP_WEBAPP_URL}/project/snap/${records.hash}`,
      '_blank'
    );
  };

  const handleFeedbackSuccess = async (data) => {
    const id = feedbackData.id;
    const payload = { id: id, feedback: data };
    try {
      await dataProvider.update(`v1/callout`, {
        method: 'PATCH',
        id: record.id,
        endPoint:
          isModeType === 'addSlateFeedback'
            ? 'feedbackSlate'
            : 'feedbackSubmission',
        data: payload,
      });
      setIsOpenModal(false);
      refresh();
      notify(`Submission feedback successfully!`);
      setReset(true);
    } catch (error) {
      setIsOpenModal(false);
      console.error('API Error:', error);
      notify(`Failed to submit submission feedback.`);
    }
  };

  const handleSubmissionSuccess = async (data) => {
    const id = isModeType !== 'editNotes' ? submissionData.id : editNotes.id;
    const payload = { id: id, notes: data };
    try {
      await dataProvider.update(`v1/callout`, {
        method: 'PATCH',
        id: record.id,
        endPoint: isModeType === 'editNotes' ? 'notesSlate' : 'slate',
        data: payload,
      });
      setIsOpenModal(false);
      refresh();
      notify(`Submitted successfully!`);
    } catch (error) {
      setIsOpenModal(false);
      console.error('API Error:', error);
      notify(`Failed to submit submission.`);
    }
  };

  //handle submit
  const handleSubmit = (data) => {
    if (isModeType === 'addToSlate' || isModeType === 'editNotes') {
      handleSubmissionSuccess(data);
    } else if (
      isModeType === 'addFeedback' ||
      isModeType === 'addSlateFeedback'
    ) {
      handleFeedbackSuccess(data);
    }
  };

  const handleStatusChange = async (newStatus, records) => {
    const payload = { id: records.id, status: newStatus };
    try {
      await dataProvider.update(`v1/callout`, {
        method: 'PATCH',
        id: record.id,
        endPoint: 'slateStatus',
        data: payload,
      });
      refresh();
      notify(`Status Updated successfully!`);
    } catch (error) {
      console.error('API Error:', error);
      notify(`Failed to update status.`);
    }
  };

  const handleEditSlat = (val, event) => {
    switch (event) {
      case 'editNotes': {
        setEditNotes(val);
        setIsOpenModal(true);
        setModeType('editNotes');
        setReset(false);
        break;
      }
      case 'addFeedback': {
        console.log('Setting slate feedback data:', val); // Debug log
        setFeedbackData(val);
        setIsOpenModal(true);
        setModeType('addSlateFeedback');
        setReset(false);
        break;
      }
      // case 'deleteSubmission': {
      //   setRejectedData({ isOpen: true, record: val });
      //   break;
      // }
      default: {
        console.log('default event', event);
      }
    }
  };
  const isEmailButtonDisabled = () => {
    if (calloutSlats && calloutSlats.length > 0) {
      return calloutSlats.every((slate) => slate.isEmailSent === true);
    }
    return true;
  };
  const emailSentSlate = async () => {
    // const payload = { id: records.id, status: newStatus };
    try {
      await dataProvider.update(`v1/callout`, {
        method: 'PATCH',
        id: record.id,
        endPoint: 'emailSentSlate',
        // data: payload,
      });
      refresh();
      notify(`Status Updated successfully!`);
    } catch (error) {
      console.error('API Error:', error);
      notify(`Failed to update status.`);
    }
  };

  const removeFromSlates = async () => {
    const payload = { id: deleteSlateId };
    try {
      await dataProvider.update(`v1/callout`, {
        method: 'PATCH',
        id: record.id,
        endPoint: 'deleteSlate',
        data: payload,
      });
      refresh();
      notify(`Slate deleted successfully!`);
      setConfirmDeleteSlateModal(false);
    } catch (error) {
      setConfirmDeleteSlateModal(false);
      console.error('API Error:', error);
      notify(`Failed to delete slate.`);
    }
  };
  //export button
  const CallOutListActions = () => (
    <TopToolbar>
      <ExportButton maxResults={1000000} />
    </TopToolbar>
  );

  return (
    <>
      <Title title={`Callout: ${record.name}`} />
      <Show title={false}>
        <SimpleShowLayout>
          <Typography variant="h6" sx={{ mb: 2, fontWeight: 'bold' }}>
            Call out details
          </Typography>
          <Box
            sx={{ mt: 4, p: 2, border: '1px solid #ddd', borderRadius: '5px' }}
          >
            <Stack
              direction="row"
              spacing={2}
              sx={{ mb: 3, justifyContent: 'space-between' }}
            >
              <Stack direction="row" spacing={4}>
                <Button
                  label="Edit call out"
                  startIcon={<Edit />}
                  onClick={() => {
                    redirect(`/callouts/${record.id}`);
                  }}
                />
                <Button
                  label={isPublished ? 'Unpublish' : 'Publish'}
                  onClick={() => setIsOpen(true)}
                  startIcon={<Visibility />}
                />
                <Confirm
                  isOpen={isOpen}
                  title={confirmTitle}
                  content={confirmContent}
                  confirm={isPublished ? 'UnPublish' : 'Publish'}
                  onConfirm={handlePublishCallout}
                  onClose={() => setIsOpen(false)}
                />
                <Button
                  label="Share call out"
                  startIcon={<Share />}
                  onClick={() => handleShareCallout()}
                />
                <Button
                  label="View call out"
                  startIcon={<Visibility />}
                  onClick={() => handleViewCallout()}
                />
              </Stack>
              <Button
                label="Send upgrade email"
                startIcon={<Email />}
                onClick={handleSendUpgradeEmail}
                sx={{ color: 'white', p: 1 }}
              />
            </Stack>
            <Box
              sx={{
                display: 'flex',
                flexWrap: 'wrap',
                gap: 3,
                '& > *': {
                  flexBasis: 'calc(25% - 24px)',
                  maxWidth: 'calc(25% - 24px)',
                },
              }}
            >
              <Box>
                <Typography variant="body2" fontWeight="bold" sx={{ mb: 2 }}>
                  Call out name
                </Typography>
                <TextField source="name" />
              </Box>

              <Box>
                <Typography variant="body2" fontWeight="bold" sx={{ mb: 2 }}>
                  Discoverer name
                </Typography>
                <TextField source="discoverer" />
              </Box>

              <Box>
                <Typography variant="body2" fontWeight="bold" sx={{ mb: 2 }}>
                  Organization name
                </Typography>
                <TextField source="companyName" />
              </Box>

              <Box>
                <Typography variant="body2" fontWeight="bold" sx={{ mb: 2 }}>
                  Discoverer email
                </Typography>
                <TextField source="email" />
              </Box>

              <Box>
                <Typography variant="body2" fontWeight="bold" sx={{ mb: 1 }}>
                  Subscription
                </Typography>
                <FunctionField
                  label={
                    <span style={{ fontWeight: 'bold' }}>Subscription</span>
                  }
                  render={(record) =>
                    record ? (
                      <ChipField
                        source="subscription"
                        record={record}
                        style={{
                          backgroundColor: getStatusBackgroundColor(
                            record.subscription
                          ),
                          textTransform: 'capitalize',
                          color: getStatusTextColor(record.subscription),
                          fontWeight: '700',
                        }}
                      />
                    ) : null
                  }
                />
                <PopoverModal
                  title={
                    isModeType === 'editNotes' || isModeType === 'addToSlate'
                      ? `Update ${get(
                          editNotes || submissionData,
                          'projectName',
                          ''
                        )} to Slate`
                      : `Send Feedback to ${get(
                          feedbackData,
                          'projectCreator',
                          get(feedbackData, 'creatorName', '')
                        )}`
                  }
                  open={isOpenModal}
                  onClose={() => {
                    setIsOpenModal(false);
                  }}
                  btnText={
                    isModeType === 'editNotes' || isModeType === 'addToSlate'
                      ? 'Update Slate'
                      : 'Send to project creator'
                  }
                  onSubmit={handleSubmit}
                  reset={reset}
                  defaultValue={
                    isModeType === 'editNotes' ? get(editNotes, 'notes') : null
                  }
                  placeholder={
                    isModeType === 'editNotes' || isModeType === 'addToSlate'
                      ? 'Update note about call out for discover'
                      : 'Send feedback to the project creator so they can update their project and submit a new snapshot'
                  }
                  previousFeedback={
                    isModeType === 'addFeedback' ||
                    isModeType === 'addSlateFeedback'
                      ? (() => {
                          // Get feedback history array if it exists
                          const feedbackHistory = get(
                            feedbackData,
                            'feedbackHistory',
                            []
                          );

                          // If no feedback history but there's old feedback, create a history entry
                          if (feedbackHistory.length === 0) {
                            const oldFeedback = get(
                              feedbackData,
                              'feedback',
                              ''
                            );
                            if (oldFeedback && oldFeedback.trim()) {
                              return [
                                {
                                  _id: 'legacy',
                                  feedback: oldFeedback,
                                  addedAt:
                                    get(feedbackData, 'updatedAt') ||
                                    get(feedbackData, 'addedAt') ||
                                    new Date(),
                                },
                              ];
                            }
                          }

                          return feedbackHistory;
                        })()
                      : null
                  }
                />

                <Confirm
                  isOpen={confirmSubmissionModal}
                  title={'Reject Submission'}
                  content={
                    'Are you sure you want to change this submission status?'
                  }
                  confirm={'Confirm'}
                  onConfirm={confirmRejectSubmission} // Calls the function when confirmed
                  onClose={() => setConfirmSubmissionModal(false)} // Closes modal
                />
                <Confirm
                  isOpen={confirmDeleteSlateModal}
                  title={'Delete Slate'}
                  content={'Are you sure you want to delete this slate?'}
                  confirm={'Confirm'}
                  onConfirm={removeFromSlates} // Calls the function when confirmed
                  onClose={() => setConfirmDeleteSlateModal(false)} // Closes modal
                />
              </Box>

              {record.geners && (
                <Box>
                  <Typography variant="body2" fontWeight="bold" sx={{ mb: 2 }}>
                    Opportunity type
                  </Typography>
                  <TextField source="genres" />
                </Box>
              )}

              {record.opportunities && (
                <Box>
                  <Typography variant="body2" fontWeight="bold" sx={{ mb: 2 }}>
                    Genres
                  </Typography>
                  <TextField source="opportunities" />
                </Box>
              )}
            </Box>
          </Box>

          <Box
            sx={{ mt: 2, p: 2, border: '1px solid #ddd', borderRadius: '5px' }}
          >
            <Stack
              direction="row"
              spacing={2}
              sx={{ justifyContent: 'space-between' }}
            >
              <Typography variant="subtitle1" fontWeight="bold">
                Slates
              </Typography>
              <Button
                label="Send slate updates to discoverer"
                startIcon={<Email />}
                onClick={emailSentSlate}
                sx={{
                  color: 'white', // Ensures text is white
                  p: 1,
                  opacity: isEmailButtonDisabled() ? 0.5 : 1,
                  '&.Mui-disabled': {
                    color: 'rgba(255, 255, 255, 0.5)', // Ensures disabled text is visible
                  },
                }}
                disabled={isEmailButtonDisabled()}
              />
            </Stack>
            <List
              actions={<CallOutListActions />}
              disableSyncWithLocation
              resource="calloutSlats"
              filter={{ calloutId: record?.id, select: 'slates' }}
              title={false}
              empty={
                <>
                  <Box textAlign="center" p={2}>
                    <InboxIcon sx={{ fontSize: 100 }} />
                    <Typography variant="h6" color="textSecondary">
                      No Slates Yet
                    </Typography>
                  </Box>
                </>
              }
              pagination={<PostPagination />}
              meta={{ noPagination: props.perPage === 100000 }}
            >
              <Datagrid bulkActionButtons={false}>
                <FunctionField
                  sortable={false}
                  source="title"
                  label={
                    <span style={{ fontWeight: 'bold' }}>Snapshot name</span>
                  }
                  render={(record) => (
                    <Link
                      className="admin-link"
                      to={`${process.env.REACT_APP_WEBAPP_URL}/project/snap/${record.hash}`}
                      target="_blank"
                      rel="noopener noreferrer"
                    >
                      {record.title}
                    </Link>
                  )}
                />

                <FunctionField
                  sortable={false}
                  source="projectName"
                  label={
                    <span style={{ fontWeight: 'bold' }}>Project Name</span>
                  }
                  render={(record) => (
                    <Link
                      className="admin-link"
                      to={`/projects/${record.projectId}/show`}
                    >
                      {record.projectName}
                    </Link>
                  )}
                />

                <FunctionField
                  sortBy="submissions.creator.username"
                  source="creatorName"
                  label={
                    <span style={{ fontWeight: 'bold' }}> Project creator</span>
                  }
                  render={(record) => (
                    <Link
                      className="admin-link"
                      to={`/users/${record.userId}/show`}
                    >
                      {record.creatorName}
                    </Link>
                  )}
                />

                <EmailField
                  source="creatorEmail"
                  label={
                    <span style={{ fontWeight: 'bold' }}>Creator email</span>
                  }
                />

                <TextField
                  sortBy="slates.submissionAddedAt"
                  label={<span style={{ fontWeight: 'bold' }}>Created</span>}
                  source="submissionAddedAt"
                />

                <TextField
                  sortBy="slates.addedAt"
                  label={
                    <span style={{ fontWeight: 'bold' }}>Added to slate</span>
                  }
                  source="addedAt"
                />

                <FunctionField
                  sortable={false}
                  label={<span style={{ fontWeight: 'bold' }}>Status</span>}
                  render={(record) => (
                    <StatusField
                      record={record}
                      onStatusChange={handleStatusChange}
                    />
                  )}
                />

                <BooleanField
                  label={<span style={{ fontWeight: 'bold' }}>Email Sent</span>}
                  source="isEmailSent"
                />

                <FunctionField
                  label={<span style={{ fontWeight: 'bold' }}>Actions</span>}
                  render={(record) => (
                    <div style={{ display: 'flex', gap: '8px' }}>
                      {/* View Icon */}
                      <IconButton
                        onClick={() => handleEditSlat(record, 'editNotes')}
                      >
                        <Edit style={{ color: '#0D0D3F' }} />
                      </IconButton>

                      {/* Chat Icon */}
                      <IconButton
                        onClick={() => handleEditSlat(record, 'addFeedback')}
                      >
                        <ChatBubbleOutline style={{ color: '#0D0D3F' }} />
                      </IconButton>

                      {/* Add Icon */}
                      <IconButton
                        onClick={() => {
                          setConfirmDeleteSlateModal(true);
                          setDeleteSlateId(record.id);
                        }}
                      >
                        <Delete style={{ color: '#0D0D3F' }} />
                      </IconButton>
                    </div>
                  )}
                />
              </Datagrid>
            </List>
          </Box>

          <Box
            sx={{ mt: 2, p: 2, border: '1px solid #ddd', borderRadius: '5px' }}
          >
            <Box
              display="flex"
              justifyContent="space-between"
              alignItems="center"
            >
              <Typography variant="subtitle1" fontWeight="bold">
                Submissions
              </Typography>
              <FormControlLabel
                control={
                  <Checkbox
                    color="secondary"
                    checked={showNewOnly}
                    onChange={(e) => setShowNewOnly(e.target.checked)}
                  />
                }
                label="Show New Only"
              />
            </Box>
            <List
              disableSyncWithLocation
              actions={<CallOutListActions />}
              resource="calloutSubmissions"
              filter={{
                calloutId: record?.id,
                select: 'submissions',
                status: showNewOnly ? 'new' : '',
              }}
              empty={
                <>
                  <Box textAlign="center" p={2}>
                    <InboxIcon sx={{ fontSize: 100 }} />
                    <Typography variant="h6" color="textSecondary">
                      No Submissions Yet
                    </Typography>
                  </Box>
                </>
              }
              title={false}
              pagination={<PostPagination />}
              meta={{ noPagination: props.perPage === 100000 }}
            >
              <Datagrid bulkActionButtons={false}>
                <FunctionField
                  sortable={false}
                  source="title"
                  label={
                    <span style={{ fontWeight: 'bold' }}>Snapshot name</span>
                  }
                  render={(record) => (
                    <Link
                      className="admin-link"
                      to={`${process.env.REACT_APP_WEBAPP_URL}/project/snap/${record.hash}`}
                      target="_blank"
                      rel="noopener noreferrer"
                    >
                      {record.title}
                    </Link>
                  )}
                />

                <FunctionField
                  sortable={false}
                  source="projectName"
                  label={
                    <span style={{ fontWeight: 'bold' }}>Project Name</span>
                  }
                  render={(record) => (
                    <Link
                      className="admin-link"
                      to={`/projects/${record.projectId}/show`}
                    >
                      {record.projectName}
                    </Link>
                  )}
                />
                <FunctionField
                  sortBy="submissions.creator.username"
                  source="creatorName"
                  label={
                    <span style={{ fontWeight: 'bold' }}> Project creator</span>
                  }
                  render={(record) => (
                    <Link
                      className="admin-link"
                      to={`/users/${record.userId}/show`}
                    >
                      {record.creatorName}
                    </Link>
                  )}
                />

                <EmailField
                  source="creatorEmail"
                  label={
                    <span style={{ fontWeight: 'bold' }}>Creator email</span>
                  }
                />
                <TextField
                  sortable={false}
                  label={<span style={{ fontWeight: 'bold' }}>genre</span>}
                  source="genre"
                />
                <TextField
                  sortBy="submissions.addedAt"
                  label={
                    <span style={{ fontWeight: 'bold' }}>Date submitted</span>
                  }
                  source="addedAt"
                />
                <FunctionField
                  sortBy="submissions.status"
                  label={<span style={{ fontWeight: 'bold' }}>Status</span>}
                  render={(record) => {
                    if (!record) return null;

                    let statusLabel = record.status;

                    if (record.status === 'cupid_selected') {
                      statusLabel = 'Cupid Selected';
                    } else if (record.status === 'FEEDBACK_SENT') {
                      statusLabel = 'Feedback Sent';
                    } else if (record.status === 'NEW') {
                      statusLabel = 'New submission';
                    }
                    return (
                      <ChipField
                        source="status"
                        record={{ ...record, status: statusLabel }}
                        style={{
                          backgroundColor: getStatusBackgroundColor(
                            record.status
                          ),
                          textTransform: 'capitalize',
                          color: getStatusTextColor(record.status),
                          fontWeight: '700',
                        }}
                      />
                    );
                  }}
                />

                <FunctionField
                  label={<span style={{ fontWeight: 'bold' }}>Actions</span>}
                  render={(record) => (
                    <div style={{ display: 'flex', gap: '8px' }}>
                      {/* View Icon */}
                      <IconButton onClick={() => handleView(record)}>
                        <Visibility style={{ color: '#0D0D3F' }} />
                      </IconButton>

                      {/* Close Icon (Trigger Confirm Modal) */}
                      <IconButton onClick={() => handleRejectClick(record.id)}>
                        <Close style={{ color: '#0D0D3F' }} />
                      </IconButton>

                      {/* Chat Icon */}
                      <IconButton
                        onClick={() => clickHandler(record, 'addFeedback')}
                      >
                        <ChatBubbleOutline style={{ color: '#0D0D3F' }} />
                      </IconButton>

                      {/* Add Icon */}
                      <IconButton
                        onClick={() => clickHandler(record, 'addToSlate')}
                      >
                        <Add style={{ color: '#0D0D3F' }} />
                      </IconButton>
                    </div>
                  )}
                />
              </Datagrid>
            </List>
          </Box>
        </SimpleShowLayout>
      </Show>
    </>
  );
};
