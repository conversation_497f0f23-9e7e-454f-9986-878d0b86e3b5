// Migration script to convert existing feedback strings to feedback history arrays
const mongoose = require('mongoose');

// Connect to MongoDB
async function connectToMongoDB() {
  try {
    await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/smash', {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });
    console.log('Connected to MongoDB');
  } catch (error) {
    console.error('MongoDB connection error:', error);
    process.exit(1);
  }
}

// Migration function
async function migrateFeedbackToHistory() {
  const Callout = mongoose.model('Callout', new mongoose.Schema({}, { strict: false }));
  
  console.log('Starting feedback migration...');
  
  try {
    // Find all callouts that have submissions or slates with feedback
    const calloutsWithFeedback = await Callout.find({
      $or: [
        { 'submissions.feedback': { $exists: true, $ne: null, $ne: '' } },
        { 'slates.feedback': { $exists: true, $ne: null, $ne: '' } }
      ]
    });

    console.log(`Found ${calloutsWithFeedback.length} callouts with feedback to migrate`);

    let migratedCount = 0;
    let submissionsMigrated = 0;
    let slatesMigrated = 0;

    for (const callout of calloutsWithFeedback) {
      let hasUpdates = false;
      const updates = {};

      // Migrate submissions feedback
      if (callout.submissions && callout.submissions.length > 0) {
        callout.submissions.forEach((submission, index) => {
          if (submission.feedback && submission.feedback.trim() && !submission.feedbackHistory) {
            // Create feedback history entry from existing feedback
            const feedbackEntry = {
              _id: new mongoose.Types.ObjectId(),
              feedback: submission.feedback,
              addedAt: submission.updatedAt || submission.addedAt || new Date(),
              addedBy: {
                userId: null,
                name: 'Admin (Migrated)',
                email: '<EMAIL>'
              }
            };

            updates[`submissions.${index}.feedbackHistory`] = [feedbackEntry];
            hasUpdates = true;
            submissionsMigrated++;
          }
        });
      }

      // Migrate slates feedback
      if (callout.slates && callout.slates.length > 0) {
        callout.slates.forEach((slate, index) => {
          if (slate.feedback && slate.feedback.trim() && !slate.feedbackHistory) {
            // Create feedback history entry from existing feedback
            const feedbackEntry = {
              _id: new mongoose.Types.ObjectId(),
              feedback: slate.feedback,
              addedAt: slate.updatedAt || slate.addedAt || new Date(),
              addedBy: {
                userId: null,
                name: 'Admin (Migrated)',
                email: '<EMAIL>'
              }
            };

            updates[`slates.${index}.feedbackHistory`] = [feedbackEntry];
            hasUpdates = true;
            slatesMigrated++;
          }
        });
      }

      // Apply updates if any
      if (hasUpdates) {
        await Callout.updateOne(
          { _id: callout._id },
          { $set: updates }
        );
        migratedCount++;
        console.log(`Migrated callout ${callout._id}: ${callout.name}`);
      }
    }

    console.log('\n=== Migration Summary ===');
    console.log(`Callouts processed: ${calloutsWithFeedback.length}`);
    console.log(`Callouts migrated: ${migratedCount}`);
    console.log(`Submissions migrated: ${submissionsMigrated}`);
    console.log(`Slates migrated: ${slatesMigrated}`);
    console.log('Migration completed successfully!');

  } catch (error) {
    console.error('Migration failed:', error);
    throw error;
  }
}

// Verification function
async function verifyMigration() {
  const Callout = mongoose.model('Callout', new mongoose.Schema({}, { strict: false }));
  
  console.log('\nVerifying migration...');
  
  try {
    // Count submissions with feedback history
    const submissionsWithHistory = await Callout.aggregate([
      { $unwind: '$submissions' },
      { $match: { 'submissions.feedbackHistory': { $exists: true, $ne: [] } } },
      { $count: 'total' }
    ]);

    // Count slates with feedback history
    const slatesWithHistory = await Callout.aggregate([
      { $unwind: '$slates' },
      { $match: { 'slates.feedbackHistory': { $exists: true, $ne: [] } } },
      { $count: 'total' }
    ]);

    console.log('=== Verification Results ===');
    console.log(`Submissions with feedback history: ${submissionsWithHistory[0]?.total || 0}`);
    console.log(`Slates with feedback history: ${slatesWithHistory[0]?.total || 0}`);
    
    // Sample check - get a few examples
    const sampleCallout = await Callout.findOne({
      $or: [
        { 'submissions.feedbackHistory': { $exists: true, $ne: [] } },
        { 'slates.feedbackHistory': { $exists: true, $ne: [] } }
      ]
    });

    if (sampleCallout) {
      console.log('\n=== Sample Migrated Data ===');
      console.log(`Callout: ${sampleCallout.name}`);
      
      if (sampleCallout.submissions) {
        sampleCallout.submissions.forEach((sub, index) => {
          if (sub.feedbackHistory && sub.feedbackHistory.length > 0) {
            console.log(`  Submission ${index + 1}: ${sub.feedbackHistory.length} feedback entries`);
          }
        });
      }
      
      if (sampleCallout.slates) {
        sampleCallout.slates.forEach((slate, index) => {
          if (slate.feedbackHistory && slate.feedbackHistory.length > 0) {
            console.log(`  Slate ${index + 1}: ${slate.feedbackHistory.length} feedback entries`);
          }
        });
      }
    }

  } catch (error) {
    console.error('Verification failed:', error);
  }
}

// Main execution
async function main() {
  console.log('🚀 Starting Feedback History Migration\n');
  
  await connectToMongoDB();
  
  try {
    await migrateFeedbackToHistory();
    await verifyMigration();
    
    console.log('\n✅ Migration completed successfully!');
    console.log('The feedback history feature is now ready to use.');
    
  } catch (error) {
    console.error('\n❌ Migration failed:', error.message);
    process.exit(1);
  } finally {
    await mongoose.disconnect();
    console.log('\n🔌 Disconnected from MongoDB');
  }
}

// Run the migration
if (require.main === module) {
  main().catch(console.error);
}

module.exports = { migrateFeedbackToHistory, verifyMigration };
